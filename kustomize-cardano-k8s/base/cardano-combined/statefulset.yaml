apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-combined
spec:
  serviceName: cardano-combined
  replicas: 1
  selector:
    matchLabels:
      app: cardano-combined
  template:
    metadata:
      labels:
        app: cardano-combined
    spec:
      initContainers:
      # Mithril bootstrap container for fast initial sync
      - name: mithril-bootstrap
        image: ghcr.io/input-output-hk/mithril-client:latest
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: MITHRIL_AGGREGATOR_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: MITHRIL_AGGREGATOR_ENDPOINT
        - name: MITHRIL_SNAPSHOT_DIGEST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: MITHRIL_SNAPSHOT_DIGEST
        - name: ENABLE_MITHRIL_BOOTSTRAP
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: ENABLE_MITHRIL_BOOTSTRAP
        command: ["/bin/bash"]
        args: ["/scripts/mithril-bootstrap.sh"]
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: mithril-scripts
          mountPath: /scripts
        - name: mithril-snapshot-cache
          mountPath: /shared
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      # Wait for PostgreSQL to be ready
      - name: wait-for-postgres
        image: postgres:17.2-alpine
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        command: ["/bin/bash"]
        args: ["/scripts/wait-for-postgres.sh"]
        volumeMounts:
        - name: cardano-combined-config
          mountPath: /scripts
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      containers:
      # Cardano Node container
      - name: cardano-node
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        ports:
        - containerPort: 3001
          name: cardano-node
          protocol: TCP
        - containerPort: 12788
          name: ekg-metrics
          protocol: TCP
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: node-ipc
          mountPath: /ipc
        - name: mithril-snapshot-cache
          mountPath: /shared
        resources:
          requests:
            memory: "5Gi"
            cpu: "2000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        livenessProbe:
          httpGet:
            path: /
            port: 12788
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 12788
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      # Cardano DB Sync container
      - name: cardano-db-sync
        image: ghcr.io/intersectmbo/cardano-db-sync:********
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        ports:
        - containerPort: 8080
          name: prometheus
          protocol: TCP
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /ipc
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        # Wait for cardano-node to be ready before starting
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "test -S /ipc/node.socket"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
      volumes:
      - name: node-ipc
        emptyDir: {}
      # Mithril bootstrap scripts
      - name: mithril-scripts
        configMap:
          name: mithril-config
          defaultMode: 0755
      # Combined configuration scripts
      - name: cardano-combined-config
        configMap:
          name: cardano-combined-config
          defaultMode: 0755
      # Shared volume for Mithril snapshot caching
      - name: mithril-snapshot-cache
        persistentVolumeClaim:
          claimName: mithril-snapshot-cache
  volumeClaimTemplates:
  - metadata:
      name: node-db
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
  - metadata:
      name: db-sync-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 4Gi
