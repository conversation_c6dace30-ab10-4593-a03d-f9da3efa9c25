apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-db-sync-config
data:
  wait-for-services.sh: |
    #!/bin/bash
    set -euo pipefail

    echo "Installing required tools..."
    apk add --no-cache netcat-openbsd

    echo "Waiting for dependencies: PostgreSQL and Cardano Node..."

    # Wait for PostgreSQL
    echo "Checking PostgreSQL..."
    until pg_isready -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} >/dev/null 2>&1; do
      echo "PostgreSQL not ready, waiting 5 seconds..."
      sleep 5
    done
    echo "PostgreSQL is ready!"

    # Wait for Cardano Node socat-tcp
    echo "Checking Cardano Node socat-tcp..."
    until nc -z ${CARDANO_NODE_SOCKET_TCP_HOST} ${CARDANO_NODE_SOCKET_TCP_PORT} >/dev/null 2>&1; do
      echo "Cardano Node socat-tcp not ready, waiting 10 seconds..."
      sleep 10
    done
    echo "Cardano Node socat-tcp is ready!"

    echo "All dependencies ready - cardano-db-sync can start!"
