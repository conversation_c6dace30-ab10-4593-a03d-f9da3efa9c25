# Storage patches for testnet - smaller storage requirements
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cardano-node
spec:
  template:
    spec:
      volumes:
      - name: node-db
        emptyDir:
          sizeLimit: 20Gi  # Testnet requires less storage
---
# Mithril snapshot cache PVC for testnet
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mithril-snapshot-cache
spec:
  resources:
    requests:
      storage: 50Gi  # Testnet requires less storage for snapshot cache

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-db-sync
spec:
  volumeClaimTemplates:
  - metadata:
      name: db-sync-data
    spec:
      storageClassName: "local-path" 
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 8Gi  # Testnet requires less storage

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
spec:
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      storageClassName: "local-path" 
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 30Gi  # Testnet database is smaller
